import { Injectable } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { BehaviorSubject, Observable } from 'rxjs';
import { IField } from '../../interfaces';

export interface FieldRegistration {
  field: IField;
  control: AbstractControl;
  path: string;
  parentId?: string;
  childrenIds: string[];
  metadata: Map<string, any>;
}

export interface FieldLookup {
  byId: Map<string, FieldRegistration>;
  byPath: Map<string, string>;
  byName: Map<string, Set<string>>;
  byType: Map<string, Set<string>>;
}

@Injectable({
  providedIn: 'root',
})
export class FieldRegistry {
  private registrations = new Map<string, FieldRegistration>();
  private lookup: FieldLookup = {
    byId: new Map(),
    byPath: new Map(),
    byName: new Map(),
    byType: new Map(),
  };

  private registrySubject = new BehaviorSubject<Map<string, FieldRegistration>>(
    new Map()
  );
  public registry$ = this.registrySubject.asObservable();

  private changeSubject = new BehaviorSubject<string[]>([]);
  public changes$ = this.changeSubject.asObservable();

  public register(
    field: IField,
    control: AbstractControl,
    path: string,
    parentId?: string
  ): string {
    const registration: FieldRegistration = {
      field,
      control,
      path,
      parentId,
      childrenIds: [],
      metadata: new Map(),
    };

    this.registrations.set(field.id, registration);
    this.lookup.byId.set(field.id, registration);
    this.lookup.byPath.set(path, field.id);

    if (!this.lookup.byName.has(field.name)) {
      this.lookup.byName.set(field.name, new Set());
    }
    this.lookup.byName.get(field.name)!.add(field.id);

    if (!this.lookup.byType.has(field.type)) {
      this.lookup.byType.set(field.type, new Set());
    }
    this.lookup.byType.get(field.type)!.add(field.id);

    if (parentId) {
      const parent = this.registrations.get(parentId);
      if (parent) {
        parent.childrenIds.push(field.id);
      }
    }

    this.notifyChange([field.id]);
    return field.id;
  }

  public unregister(fieldId: string): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;

    registration.childrenIds.forEach((childId) => this.unregister(childId));

    this.registrations.delete(fieldId);
    this.lookup.byId.delete(fieldId);
    this.lookup.byPath.delete(registration.path);

    const nameSet = this.lookup.byName.get(registration.field.name);
    if (nameSet) {
      nameSet.delete(fieldId);
      if (nameSet.size === 0) {
        this.lookup.byName.delete(registration.field.name);
      }
    }

    const typeSet = this.lookup.byType.get(registration.field.type);
    if (typeSet) {
      typeSet.delete(fieldId);
      if (typeSet.size === 0) {
        this.lookup.byType.delete(registration.field.type);
      }
    }

    if (registration.parentId) {
      const parent = this.registrations.get(registration.parentId);
      if (parent) {
        const index = parent.childrenIds.indexOf(fieldId);
        if (index > -1) {
          parent.childrenIds.splice(index, 1);
        }
      }
    }

    this.notifyChange([fieldId]);
    return true;
  }

  public get(fieldId: string): FieldRegistration | undefined {
    return this.registrations.get(fieldId);
  }

  public getByPath(path: string): FieldRegistration | undefined {
    const fieldId = this.lookup.byPath.get(path);
    return fieldId ? this.registrations.get(fieldId) : undefined;
  }

  public getByName(name: string): FieldRegistration[] {
    const ids = this.lookup.byName.get(name);
    if (!ids) return [];

    return Array.from(ids)
      .map((id) => this.registrations.get(id))
      .filter((reg) => reg !== undefined);
  }

  public getByType(type: string): FieldRegistration[] {
    const ids = this.lookup.byType.get(type);
    if (!ids) return [];

    return Array.from(ids)
      .map((id) => this.registrations.get(id))
      .filter((reg) => reg !== undefined);
  }

  public getControl(fieldId: string): AbstractControl | undefined {
    return this.registrations.get(fieldId)?.control;
  }

  public getField(fieldId: string): IField | undefined {
    return this.registrations.get(fieldId)?.field;
  }

  public getChildren(parentId: string): FieldRegistration[] {
    const parent = this.registrations.get(parentId);
    if (!parent) return [];

    return parent.childrenIds
      .map((id) => this.registrations.get(id))
      .filter((reg) => reg !== undefined);
  }

  public getParent(fieldId: string): FieldRegistration | undefined {
    const registration = this.registrations.get(fieldId);
    if (!registration?.parentId) return undefined;

    return this.registrations.get(registration.parentId);
  }

  public getSiblings(fieldId: string): FieldRegistration[] {
    const parent = this.getParent(fieldId);
    if (!parent) return [];

    return parent.childrenIds
      .filter((id) => id !== fieldId)
      .map((id) => this.registrations.get(id))
      .filter((reg) => reg !== undefined);
  }

  public updateField(fieldId: string, updates: Partial<IField>): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;

    Object.assign(registration.field, updates);
    this.notifyChange([fieldId]);
    return true;
  }

  public setMetadata(fieldId: string, key: string, value: any): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;

    registration.metadata.set(key, value);
    this.notifyChange([fieldId]);
    return true;
  }

  public getMetadata(fieldId: string, key: string): any {
    return this.registrations.get(fieldId)?.metadata.get(key);
  }

  public deleteMetadata(fieldId: string, key: string): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;

    const deleted = registration.metadata.delete(key);
    if (deleted) {
      this.notifyChange([fieldId]);
    }
    return deleted;
  }

  public findFields(
    predicate: (registration: FieldRegistration) => boolean
  ): FieldRegistration[] {
    const results: FieldRegistration[] = [];

    this.registrations.forEach((registration) => {
      if (predicate(registration)) {
        results.push(registration);
      }
    });

    return results;
  }

  public getRequiredFields(): FieldRegistration[] {
    return this.findFields((reg) => reg.field.required === true);
  }

  public getDisabledFields(): FieldRegistration[] {
    return this.findFields((reg) => reg.control.disabled);
  }

  public getInvalidFields(): FieldRegistration[] {
    return this.findFields((reg) => reg.control.invalid && reg.control.touched);
  }

  public getAllFields(): FieldRegistration[] {
    return Array.from(this.registrations.values());
  }

  public getFieldTree(rootId?: string): any {
    if (rootId) {
      const root = this.registrations.get(rootId);
      if (!root) return null;
      return this.buildFieldNode(root);
    }

    const roots = this.findFields((reg) => !reg.parentId);
    return roots.map((root) => this.buildFieldNode(root));
  }

  private buildFieldNode(registration: FieldRegistration): any {
    return {
      id: registration.field.id,
      name: registration.field.name,
      type: registration.field.type,
      path: registration.path,
      valid: registration.control.valid,
      value: registration.control.value,
      children: registration.childrenIds
        .map((childId) => {
          const child = this.registrations.get(childId);
          return child ? this.buildFieldNode(child) : null;
        })
        .filter((node) => node !== null),
    };
  }

  public moveField(
    fieldId: string,
    newParentId?: string,
    index?: number
  ): boolean {
    const registration = this.registrations.get(fieldId);
    if (!registration) return false;

    if (registration.parentId) {
      const oldParent = this.registrations.get(registration.parentId);
      if (oldParent) {
        const idx = oldParent.childrenIds.indexOf(fieldId);
        if (idx > -1) {
          oldParent.childrenIds.splice(idx, 1);
        }
      }
    }

    registration.parentId = newParentId;

    if (newParentId) {
      const newParent = this.registrations.get(newParentId);
      if (newParent) {
        if (
          index !== undefined &&
          index >= 0 &&
          index <= newParent.childrenIds.length
        ) {
          newParent.childrenIds.splice(index, 0, fieldId);
        } else {
          newParent.childrenIds.push(fieldId);
        }
      }
    }

    this.notifyChange([fieldId]);
    return true;
  }

  public clear(): void {
    const fieldIds = Array.from(this.registrations.keys());
    this.registrations.clear();
    this.lookup.byId.clear();
    this.lookup.byPath.clear();
    this.lookup.byName.clear();
    this.lookup.byType.clear();

    this.notifyChange(fieldIds);
  }

  private notifyChange(fieldIds: string[]): void {
    this.registrySubject.next(new Map(this.registrations));
    this.changeSubject.next(fieldIds);
  }

  public getStats(): any {
    return {
      totalFields: this.registrations.size,
      byType: Object.fromEntries(
        Array.from(this.lookup.byType.entries()).map(([type, ids]) => [
          type,
          ids.size,
        ])
      ),
      required: this.getRequiredFields().length,
      disabled: this.getDisabledFields().length,
      invalid: this.getInvalidFields().length,
    };
  }
}
